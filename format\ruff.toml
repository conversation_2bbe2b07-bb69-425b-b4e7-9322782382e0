# Never enforce `E501` (line length violations).
lint.ignore = ["C901", "E501", "E741", "W605", "E402"]
lint.select = ["C", "E", "F", "I", "W"]
line-length = 119

# Ignore import violations in all `__init__.py` files.
[lint.per-file-ignores]
"__init__.py" = ["E402", "F401", "F403", "F811"]

[lint.isort]
lines-after-imports = 2
known-first-party = ["gptqmodel"]

[format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
