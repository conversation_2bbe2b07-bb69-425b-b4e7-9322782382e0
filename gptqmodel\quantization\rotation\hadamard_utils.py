import math

import torch
from gptqmodel.utils.logger import setup_logger

# Adapted from https://github.com/Cornell-RelaxML/quip-sharp/blob/main/lib/utils/matmul_had.py

log = setup_logger()

def get_hadK(n, transpose=False):
    hadK, K = None, None
    if n % 172 == 0:  # llama-2-7b up
        assert is_pow2(n // 172)
        K = 172
        hadK = get_had172().T if transpose else get_had172()
    elif n % 156 == 0:  # llama-1-30b 3x hidden
        assert is_pow2(n // 156)
        K = 156
        hadK = get_had156().T if transpose else get_had156()
    elif n % 140 == 0:  # llama-1-30b intermediate
        assert is_pow2(n // 140)
        K = 140
        hadK = get_had140().T if transpose else get_had140()
    elif n % 108 == 0:  # llama-1-13b intermediate
        assert is_pow2(n // 108)
        K = 108
        hadK = get_had108().T if transpose else get_had108()
    elif n % 60 == 0:  # llama-1-13b 3x hidden
        assert is_pow2(n // 60)
        K = 60
        hadK = get_had60().T if transpose else get_had60()
    elif n % 52 == 0:  # llama-1-13b 1x hidden
        assert is_pow2(n // 52)
        K = 52
        hadK = get_had52().T if transpose else get_had52()
    elif n % 36 == 0:
        assert is_pow2(n // 36)
        K = 36
        hadK = get_had36().T if transpose else get_had36()
    elif n % 28 == 0:  # llama-3 up
        assert is_pow2(n // 28)
        K = 28
        hadK = get_had28().T if transpose else get_had28()
    elif n % 40 == 0:
        assert is_pow2(n // 40)
        K = 40
        hadK = get_had40().T if transpose else get_had40()
    elif n % 20 == 0:
        assert is_pow2(n // 20)
        K = 20
        hadK = get_had20().T if transpose else get_had20()
    elif n % 12 == 0:
        assert is_pow2(n // 12)
        K = 12
        hadK = get_had12().T if transpose else get_had12()
    else:
        assert is_pow2(n)
        K = 1

    return hadK, K


def matmul_hadU(X, transpose=False):
    n = X.shape[-1]
    hadK, K = get_hadK(n, transpose)
    input = X.clone().view(-1, n, 1)
    output = input.clone()
    while input.shape[1] > K:
        input = input.view(input.shape[0], input.shape[1] // 2, 2, input.shape[2])
        output = output.view(input.shape)
        output[:, :, 0, :] = input[:, :, 0, :] + input[:, :, 1, :]
        output[:, :, 1, :] = input[:, :, 0, :] - input[:, :, 1, :]
        output = output.view(input.shape[0], input.shape[1], -1)
        (input, output) = (output, input)
    del output

    if K > 1:
        # Do not explicitly repeat - OOM
        # input = torch.bmm(
        #     hadK.repeat(len(input), 1, 1).to(input.device).to(input.dtype), input)
        # Use bcast instead
        input = hadK.view(1, K, K).to(input) @ input

    return input.view(X.shape) / torch.tensor(n).sqrt()


def matmul_hadUt(X):
    return matmul_hadU(X, transpose=True)


def random_hadamard_matrix(size, device):
    # See https://cornell-relaxml.github.io/quip-sharp/ , Section "Randomized Hadamard Transformation"
    Q = torch.randint(low=0, high=2, size=(size,)).to(torch.float64)
    Q = Q * 2 - 1
    Q = torch.diag(Q)
    return matmul_hadU(Q).to(device)

# TODO make this a util
def import_fast_hadamard_transform():
    try:
        import fast_hadamard_transform  # noqa: F401
    except ImportError as e:
        log.error("Package: Please install missing `fast_hadamard_transform` module via: `pip install -U git+https://github.com/Dao-AILab/fast-hadamard-transform.git --no-build-isolation -v`")
        raise e

def matmul_hadU_cuda(X, hadK, K):
    import_fast_hadamard_transform()

    n = X.shape[-1]
    if K == 1:
        return fast_hadamard_transform.hadamard_transform( # noqa: F821
            X.contiguous(), 1.0 / torch.tensor(n).sqrt()
        )
    # if transpose:
    #     hadK = hadK.T.contiguous()
    input = X.view(-1, K, n // K)
    input = fast_hadamard_transform.hadamard_transform( # noqa: F821
        input.contiguous(), 1.0 / torch.tensor(n).sqrt()
    )
    input = hadK.to(input.device).to(input.dtype) @ input
    return input.reshape(X.shape)


# def matmul_hadUt_cuda(X, hadK, K):
#     return matmul_hadU_cuda(X, hadK, K, transpose=True)


def apply_exact_had_to_linear(module, had_dim=-1, output=False):
    import_fast_hadamard_transform()

    assert isinstance(module, torch.nn.Linear)
    in_features, out_features = module.in_features, module.out_features

    if had_dim != -1:
        assert is_pow2(had_dim), "Hadamard dimension must be a power of 2!"

    # weight
    W_ = module.weight.data
    dtype = W_.dtype
    dev = W_.device
    init_shape = W_.shape
    W_ = W_.float().cuda()

    # bias
    # bias needs to apply hadamard when output=True
    if module.bias is not None:
        b_ = module.bias.data
        b_dtype = b_.dtype
        b_init_shape = b_.shape
        b_ = b_.unsqueeze(0) if len(b_init_shape) == 1 else b_
        b_ = b_.float().cuda()

    if had_dim == -1:
        if output:
            had_K, K = get_hadK(out_features)
            W_ = matmul_hadU_cuda(W_.t(), had_K, K).t()
            if module.bias is not None:
                b_ = matmul_hadU_cuda(b_, had_K, K).reshape(b_init_shape)
        if not output:
            had_K, K = get_hadK(in_features)
            W_ = matmul_hadU_cuda(W_, had_K, K)
    else:
        # Apply Hadamard to the last had_dim chunks of the weights
        if output:
            W_ = W_.t()
            transposed_shape = W_.shape
            W_ = (
                fast_hadamard_transform.hadamard_transform( # noqa: F821
                    W_.reshape(-1, transposed_shape[-1] // had_dim, had_dim),
                    scale=1 / math.sqrt(had_dim),
                )
                .reshape(transposed_shape)
                .t()
                .contiguous()
            )
            if module.bias is not None:
                b_ = (
                    fast_hadamard_transform.hadamard_transform( # noqa: F821
                        b_.reshape(-1, b_.shape[-1] // had_dim, had_dim),
                        scale=1 / math.sqrt(had_dim),
                    )
                    .reshape(b_init_shape)
                    .contiguous()
                )

        else:
            W_ = fast_hadamard_transform.hadamard_transform( # noqa: F821
                W_.reshape(-1, init_shape[1] // had_dim, had_dim),
                scale=1 / math.sqrt(had_dim),
            ).reshape(init_shape)
    module.weight.data = W_.to(device=dev, dtype=dtype)
    if module.bias is not None:
        module.bias.data = b_.to(device=dev, dtype=b_dtype)


def is_pow2(n):
    return (n & (n - 1) == 0) and (n > 0)


# hadamard matrices for had12, had36.pal2, had52,will,
# # had60.pal, had108.pal, had140.pal, had156.will, had172.will:
# http://www.neilsloane.com/hadamard/index.html
def get_had12():
    return torch.FloatTensor([
        [+1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1],
        [+1, +1, -1, +1, -1, -1, -1, +1, +1, +1, -1, +1],
        [+1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1, -1],
        [+1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1],
        [+1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1],
        [+1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1],
        [+1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1],
        [+1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1],
        [+1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1],
        [+1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1],
        [+1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1],
        [+1, -1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1],
    ])


def get_had40():
    return torch.FloatTensor([
        [
            +1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1,
            -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, ],
        [
            +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
            -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, ],
        [
            +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1,
            -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, ],
        [
            +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
            -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, ],
        [
            +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
            +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, ],
        [
            +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
            +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, ],
        [
            +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
            -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, ],
        [
            +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
            +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, ],
        [
            +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
            +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, ],
        [
            +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1,
            -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, ],
        [
            +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1,
            -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, ],
        [
            +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
            +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, ],
        [
            +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
            +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, ],
        [
            +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
            +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, ],
        [
            +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
            +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, ],
        [
            +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
            -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, ],
        [
            +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1,
            +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, ],
        [
            +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1,
            -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, ],
        [
            +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1,
            +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, ],
        [
            +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1,
            -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, ],
        [
            +1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1,
            +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, ],
        [
            +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
            +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, ],
        [
            +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1,
            +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, ],
        [
            +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1,
            +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, ],
        [
            +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1,
            -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, ],
        [
            +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1,
            -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, ],
        [
            +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1,
            +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, ],
        [
            +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1,
            -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, ],
        [
            +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1,
            -1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, ],
        [
            +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
            +1, -1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, ],
        [
            +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1,
            +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, ],
        [
            +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1,
            -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, +1, -1, ],
        [
            +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
            -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, +1, ],
        [
            +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1,
            -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, ],
        [
            +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1,
            -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, ],
        [
            +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, -1, +1, -1,
            +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, ],
        [
            +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1,
            -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, ],
        [
            +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1,
            +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, ],
        [
            +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, +1, +1,
            -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, ],
        [
            +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1,
            +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, ],
    ])


def get_had20():
    return torch.FloatTensor([
        [+1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1],
        [+1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1],
        [+1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1],
        [+1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1],
        [+1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1],
        [+1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1],
        [+1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1],
        [+1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1],
        [+1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1],
        [+1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1],
        [+1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1],
        [+1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1],
        [+1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1],
        [+1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1],
        [+1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1],
        [+1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1],
        [+1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1],
        [+1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1],
        [+1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1]
    ])


def get_had28():
    return torch.FloatTensor([
        [
            +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1,
            +1, +1, +1, +1, +1, +1, +1],
        [
            +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
            -1, -1, +1, +1, -1, +1
        ],
        [
            +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
            -1, -1, -1, -1, +1, +1, -1
        ],
        [
            +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1,
            +1, -1, -1, -1, -1, +1, +1
        ],
        [
            +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, -1,
            +1, +1, -1, -1, -1, -1, +1
        ],
        [
            +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1,
            -1, +1, +1, -1, -1, -1, -1
        ],
        [
            +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, -1,
            +1, -1, +1, +1, -1, -1, -1
        ],
        [
            +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1,
            -1, +1, -1, +1, +1, -1, -1
        ],
        [
            +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1,
            +1, -1, +1, -1, +1, +1, -1
        ],
        [
            +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, +1,
            -1, +1, -1, +1, -1, +1, +1
        ],
        [
            +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1,
            +1, -1, +1, -1, +1, -1, +1
        ],
        [
            +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1,
            +1, +1, -1, +1, -1, +1, -1
        ],
        [
            +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1,
            -1, +1, +1, -1, +1, -1, +1
        ],
        [
            +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1,
            -1, -1, +1, +1, -1, +1, -1
        ],
        [
            -1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1,
            -1, -1, -1, -1, -1, -1, -1
        ],
        [
            +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
            +1, +1, +1, -1, -1, +1, -1
        ],
        [
            +1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
            +1, +1, +1, +1, -1, -1, +1
        ],
        [
            +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1,
            -1, +1, +1, +1, +1, -1, -1
        ],
        [
            +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1,
            -1, -1, +1, +1, +1, +1, -1
        ],
        [
            +1, +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
            +1, -1, -1, +1, +1, +1, +1
        ],
        [
            +1, -1, +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1,
            -1, +1, -1, -1, +1, +1, +1
        ],
        [
            +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
            -1, -1, +1, -1, -1, +1, +1
        ],
        [
            +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1,
            -1, -1, -1, +1, -1, -1, +1
        ],
        [
            +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, -1,
            +1, -1, -1, -1, +1, -1, -1
        ],
        [
            +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1,
            -1, +1, -1, -1, -1, +1, -1
        ],
        [
            +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1,
            -1, -1, +1, -1, -1, -1, +1
        ],
        [
            +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
            +1, -1, -1, +1, -1, -1, -1
        ],
        [
            +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, +1,
            +1, +1, -1, -1, +1, -1, -1
        ]])


def get_had36():
    return torch.FloatTensor([
        [+1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, +1, +1, +1,
         +1, +1, +1, +1, +1, +1, +1, +1],
        [+1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, +1, +1],
        [+1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, +1],
        [+1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1],
        [+1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, -1, +1, +1, -1, -1, -1, +1],
        [+1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, -1, +1, +1, -1, -1, -1],
        [+1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, -1, +1, +1, -1, -1],
        [+1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, -1, +1, +1, -1],
        [+1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, -1, -1, +1, +1],
        [+1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, -1, -1, +1],
        [+1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, -1, -1],
        [+1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, -1],
        [+1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1],
        [+1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, +1],
        [+1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1],
        [+1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1],
        [+1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1],
        [+1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, +1, +1, -1],
        [-1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1],
        [+1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1,
         -1, +1, +1, +1, -1, +1, -1, -1],
        [+1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, +1,
         -1, -1, +1, +1, +1, -1, +1, -1],
        [+1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         +1, -1, -1, +1, +1, +1, -1, +1],
        [+1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, +1, -1, -1, +1, +1, +1, -1],
        [+1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, +1, -1, -1, +1, +1, +1],
        [+1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, -1, -1, +1, +1],
        [+1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, +1, -1, -1, +1],
        [+1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, +1, -1, -1],
        [+1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, +1, -1],
        [+1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, +1],
        [+1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1],
        [+1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1],
        [+1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1],
        [+1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, +1, +1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1],
        [+1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, +1, +1,
         +1, -1, +1, -1, -1, -1, -1, -1],
        [+1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, +1,
         +1, +1, -1, +1, -1, -1, -1, -1],
        [+1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, -1,
         +1, +1, +1, -1, +1, -1, -1, -1],
    ])


def get_had60():
    return torch.FloatTensor([
        [+1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, ],
        [+1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, ],
        [+1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, -1, ],
        [+1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, ],
        [+1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, ],
        [+1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, +1, ],
        [+1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, ],
        [+1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, ],
        [+1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, -1, -1, +1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, -1, -1, ],
        [+1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, -1, ],
        [+1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, ],
        [+1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, ],
        [+1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, +1, +1, -1, ],
        [+1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, +1, +1, ],
        [+1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, +1, ],
        [+1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, ],
        [+1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, ],
        [+1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, +1, +1, ],
        [+1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, +1, ],
        [+1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, ],
        [+1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, ],
        [+1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, ],
        [+1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         +1, +1, +1, -1, ],
        [+1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, +1, +1, +1, ],
        [+1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, +1, +1, ],
        [+1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, +1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, ],
        [+1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, ],
        [+1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, ],
        [+1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, -1, -1, ],
        [+1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, -1, ],
        [+1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, ],
        [+1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         -1, +1, +1, -1, ],
        [+1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, -1, +1, +1, ],
        [+1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, -1, +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, -1, ],
        [+1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, ],
        [+1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         -1, -1, +1, -1, ],
        [+1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, -1, -1, +1, ],
        [+1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, -1, -1, ],
        [+1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, -1, ],
        [+1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, ],
        [+1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, ],
        [+1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, -1, +1, ],
        [+1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, -1, ],
        [+1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, ],
        [+1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1,
         -1, +1, -1, +1, ],
        [+1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, -1, ],
        [+1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, ],
        [+1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, -1, +1, ],
        [+1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, -1, ],
        [+1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, ],
        [+1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, +1, -1, ],
        [+1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, +1, ],
        [+1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, ],
        [+1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, ],
    ])


def get_had52():
    return torch.FloatTensor([
        [+1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1,
         -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, ],
        [-1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, ],
        [+1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, ],
        [-1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, ],
        [-1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, ],
        [+1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, -1, -1,
         +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, ],
        [+1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, -1,
         -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, ],
        [+1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, ],
        [+1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, +1,
         +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, ],
        [-1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, ],
        [-1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, ],
        [+1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, ],
        [-1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1, -1,
         +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, ],
        [-1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, ],
        [+1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, ],
        [+1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, ],
        [+1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, ],
        [-1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1,
         +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, ],
        [-1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, ],
        [-1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, ],
        [-1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, ],
        [-1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, ],
        [-1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, ],
        [+1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, ],
        [+1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, ],
        [-1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, ],
        [-1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, ],
        [+1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, ],
        [-1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, ],
        [+1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, ],
        [+1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, ],
        [-1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, ],
        [-1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, ],
        [+1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, ],
        [-1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1,
         +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, ],
        [+1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, ],
        [-1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, ],
        [-1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, ],
        [+1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, ],
        [+1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, ],
        [+1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, ],
        [-1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, ],
        [+1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, ],
        [+1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, ],
        [-1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, ],
        [+1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, ],
        [+1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, ],
        [+1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, ],
        [+1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, ],
    ])


def get_had108():
    return torch.FloatTensor([
        [+1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, ],
        [+1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, ],
        [+1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, ],
        [+1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, ],
        [+1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, ],
        [+1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, ],
        [+1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, ],
        [+1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, ],
        [+1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, ],
        [+1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, ],
        [+1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, ],
        [+1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, ],
        [+1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, ],
        [+1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, ],
        [+1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, ],
        [+1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, ],
        [+1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, ],
        [+1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, ],
        [+1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, ],
        [+1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, ],
        [+1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, ],
        [+1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, ],
        [+1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, ],
        [+1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, ],
        [+1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, ],
        [+1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, ],
        [+1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, ],
        [+1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, ],
        [+1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, ],
        [+1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, ],
        [+1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, ],
        [+1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, ],
        [+1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, ],
        [+1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, ],
        [+1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, ],
        [+1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, ],
        [+1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, ],
        [+1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, ],
        [+1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, ],
        [+1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, ],
        [+1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, ],
        [+1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, ],
        [+1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, ],
        [+1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, ],
        [+1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, ],
        [+1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, ],
        [+1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, ],
        [+1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, ],
        [+1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, ],
        [+1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, ],
        [+1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, ],
        [+1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, ],
        [+1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, ],
        [+1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, ],
        [+1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, ],
        [+1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, ],
        [+1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, ],
        [+1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, ],
        [+1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, ],
        [+1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, ],
        [+1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, ],
        [+1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, ],
        [+1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, ],
        [+1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, ],
        [+1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, ],
        [+1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, ],
        [+1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, ],
        [+1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, ],
        [+1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, ],
        [+1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, ],
        [+1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, ],
        [+1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, ],
        [+1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, ],
        [+1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, ],
        [+1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, ],
        [+1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, ],
        [+1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, ],
        [+1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, ],
        [+1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, ],
        [+1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, ],
        [+1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, ],
        [+1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, ],
        [+1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, ],
        [+1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, +1, ],
        [+1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, ],
        [+1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, ],
        [+1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, ],
        [+1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, ],
        [+1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, ],
        [+1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, ],
    ])


def get_had140():
    return torch.FloatTensor([
        [+1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
         -1, ],
        [+1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, ],
        [+1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, ],
        [+1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, ],
        [+1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, ],
        [+1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, ],
        [+1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1,
         -1, ],
        [+1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         +1, ],
        [+1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, ],
        [+1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, ],
        [+1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, ],
        [+1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, ],
        [+1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, ],
        [+1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, ],
        [+1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, ],
        [+1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, ],
        [+1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, ],
        [+1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, ],
        [+1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, ],
        [+1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, ],
        [+1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, ],
        [+1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         +1, ],
        [+1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, ],
        [+1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1,
         +1, ],
        [+1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, ],
        [+1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, ],
        [+1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, ],
        [+1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, ],
        [+1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, ],
        [+1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, ],
        [+1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, ],
        [+1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1,
         -1, ],
        [+1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         -1, ],
        [+1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, ],
        [+1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, ],
        [+1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, ],
        [+1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, ],
        [+1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, ],
        [+1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, ],
        [+1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         +1, ],
        [+1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, ],
        [+1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         +1, ],
        [+1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, ],
        [+1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, ],
        [+1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, ],
        [+1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, ],
        [+1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, ],
        [+1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, ],
        [+1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, ],
        [+1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, ],
        [+1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, ],
        [+1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, ],
        [+1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, ],
        [+1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, ],
        [+1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, ],
        [+1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, ],
        [+1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, ],
        [+1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, ],
        [+1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, ],
        [+1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, ],
        [+1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, ],
        [+1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, ],
        [+1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, ],
        [+1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, ],
        [+1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, ],
        [+1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, ],
        [+1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         -1, ],
        [+1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, ],
        [+1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, ],
        [+1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, ],
        [+1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, ],
        [+1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, ],
        [+1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, ],
        [+1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, ],
        [+1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         +1, ],
        [+1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, ],
        [+1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, ],
        [+1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, ],
        [+1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, ],
        [+1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, ],
        [+1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         -1, ],
        [+1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, ],
        [+1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, ],
        [+1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, ],
        [+1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, ],
        [+1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, ],
        [+1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, ],
        [+1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, ],
        [+1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, ],
        [+1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, ],
        [+1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, ],
        [+1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1,
         -1, ],
        [+1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         +1, ],
        [+1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, ],
        [+1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, ],
        [+1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         -1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, ],
        [+1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, ],
        [+1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, ],
        [+1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, ],
        [+1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1,
         +1, ],
        [+1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1,
         -1, ],
        [+1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1,
         +1, ],
        [+1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1,
         +1, ],
        [+1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         -1, ],
        [+1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, ],
        [+1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, ],
        [+1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, ],
        [+1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, ],
        [+1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1,
         +1, ],
        [+1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1,
         -1, ],
        [+1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1,
         -1, ],
        [+1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         -1, ],
        [+1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, ],
        [+1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, ],
        [+1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, ],
        [+1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, ],
    ])


def get_had156():
    return torch.FloatTensor([
        [+1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, ],
        [+1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, ],
        [+1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, ],
        [-1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, ],
        [-1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, ],
        [+1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, ],
        [-1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, ],
        [+1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, ],
        [-1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, ],
        [-1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, ],
        [-1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, ],
        [-1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, ],
        [-1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, ],
        [+1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, ],
        [-1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, ],
        [-1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, ],
        [+1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, ],
        [+1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, ],
        [-1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, ],
        [-1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, ],
        [-1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, ],
        [-1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, ],
        [+1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, ],
        [+1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, ],
        [-1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, ],
        [-1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         -1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, -1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, ],
        [-1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, ],
        [-1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, ],
        [-1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, ],
        [-1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, ],
        [-1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, ],
        [+1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, ],
        [-1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, ],
        [+1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, ],
        [-1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, ],
        [-1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, ],
        [+1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, ],
        [+1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, ],
        [-1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, ],
        [-1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, ],
        [-1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1,
         -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, ],
        [-1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1,
         +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1,
         -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, ],
        [+1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1,
         -1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, ],
        [+1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, ],
        [-1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1,
         +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, ],
        [+1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, ],
        [+1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1,
         +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, ],
        [-1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, ],
        [-1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, ],
        [+1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, ],
        [+1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, +1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, +1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, ],
        [-1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, ],
        [+1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1,
         +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, ],
        [-1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, ],
        [-1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, -1,
         -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, ],
        [+1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, ],
        [-1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1,
         -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1,
         +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, ],
        [+1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, ],
        [+1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, ],
        [+1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, ],
        [-1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, +1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, ],
        [-1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, ],
        [+1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, ],
        [+1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, ],
        [-1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, ],
        [+1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, ],
        [+1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, ],
        [-1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, ],
        [-1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, ],
        [-1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, ],
        [-1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1,
         +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, ],
        [-1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, ],
        [-1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, ],
        [+1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, ],
        [+1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, ],
        [-1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, ],
        [-1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, ],
        [-1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, ],
        [+1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, ],
        [+1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, ],
        [+1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, ],
        [-1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, ],
        [+1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, ],
        [-1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, ],
        [+1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, ],
        [-1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, ],
        [+1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, ],
        [+1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, ],
        [+1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, ],
        [+1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, ],
        [-1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, ],
        [+1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, ],
        [+1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, ],
        [-1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, ],
        [+1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, ],
        [-1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, ],
        [+1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, ],
        [+1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, ],
        [+1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, ],
        [-1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, ],
        [+1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1, +1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, ],
        [-1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, +1,
         +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, ],
        [-1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1,
         +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, +1, ],
        [+1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1,
         -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, ],
        [+1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, ],
        [-1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, -1,
         +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, ],
        [-1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1,
         -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, -1, -1, ],
        [-1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1,
         +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, ],
        [+1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1,
         +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1,
         +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, ],
        [+1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1,
         +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, ],
        [-1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1,
         -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, ],
        [-1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1,
         -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, ],
        [+1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1,
         -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, ],
        [-1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1,
         +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, +1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, ],
        [+1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1,
         -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, +1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, ],
        [-1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1,
         -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, ],
        [+1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1,
         +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, ],
        [+1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, ],
        [+1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, ],
        [+1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, ],
        [+1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, ],
        [-1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, ],
        [-1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, ],
        [-1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, ],
        [+1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, ],
        [-1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, ],
        [-1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, ],
        [+1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, ],
        [-1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, ],
        [-1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, ],
        [-1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, ],
        [+1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, ],
        [+1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, ],
        [+1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, ],
        [+1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, ],
        [+1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, ],
        [-1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, ],
        [+1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, ],
        [-1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, +1, ],
        [-1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1,
         +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, -1, ],
        [-1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1,
         +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, -1, ],
        [+1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1,
         +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1,
         +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, ],
        [+1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1,
         +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, ],
        [+1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, ],
    ])


def get_had172():
    return torch.FloatTensor([
        [+1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, +1, ],
        [-1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1,
         +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, ],
        [-1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1,
         +1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, ],
        [-1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1,
         +1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, ],
        [+1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, ],
        [+1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, ],
        [-1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, ],
        [-1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, ],
        [+1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, ],
        [+1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, ],
        [-1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, ],
        [+1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1,
         +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, ],
        [-1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1,
         +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1,
         -1, +1, +1, -1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1,
         -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1,
         -1, -1, +1, +1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1,
         +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         -1, -1, -1, +1, ],
        [+1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1,
         +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, -1, -1, -1, ],
        [-1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1,
         +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, -1, -1, ],
        [+1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, -1, ],
        [+1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, ],
        [-1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, ],
        [-1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, ],
        [+1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, ],
        [-1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, ],
        [+1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         +1, -1, -1, +1, ],
        [+1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, +1, -1, -1, ],
        [+1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, +1, -1, ],
        [-1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, +1, ],
        [+1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, ],
        [-1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, +1, -1, +1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1,
         +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, +1, -1, ],
        [+1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1,
         +1, +1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, +1, ],
        [+1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, ],
        [-1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, +1, +1, ],
        [-1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         -1, -1, -1, +1, ],
        [+1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, -1, -1, -1, ],
        [+1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, -1, -1, ],
        [-1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, -1, ],
        [-1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, ],
        [-1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, -1, +1, +1, ],
        [-1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, +1, ],
        [-1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, ],
        [+1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, ],
        [-1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, +1, ],
        [-1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, ],
        [-1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, ],
        [-1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, -1, -1, +1, ],
        [-1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, -1, -1, ],
        [-1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, -1, ],
        [+1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, ],
        [+1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, ],
        [+1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, ],
        [+1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, ],
        [-1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, +1, +1, ],
        [+1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, ],
        [-1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, ],
        [+1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, ],
        [+1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, ],
        [-1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, ],
        [-1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, -1, ],
        [-1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, ],
        [-1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1,
         -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, ],
        [+1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, ],
        [-1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, ],
        [-1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, ],
        [+1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
         +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, ],
        [+1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, ],
        [-1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, +1, ],
        [+1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1,
         -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, ],
        [-1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, +1, ],
        [+1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, ],
        [+1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, ],
        [+1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, -1, ],
        [+1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, ],
        [-1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, ],
        [-1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, +1,
         -1, +1, -1, +1, ],
        [-1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         +1, -1, +1, -1, ],
        [-1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, +1, -1, +1, ],
        [-1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, ],
        [-1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, +1, ],
        [+1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, ],
        [-1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, ],
        [-1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, +1, -1, ],
        [-1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, +1, ],
        [-1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, ],
        [+1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, ],
        [-1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, -1, ],
        [+1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1,
         -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, ],
        [-1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, ],
        [-1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, ],
        [+1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, ],
        [+1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
         +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, ],
        [-1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, +1, ],
        [+1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, ],
        [-1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1,
         -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, ],
        [+1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1,
         +1, +1, -1, +1, ],
        [-1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, ],
        [-1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1,
         -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, ],
        [-1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1,
         -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, -1, +1, ],
        [-1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1,
         -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         -1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, ],
        [-1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, -1, +1, ],
        [+1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, -1, ],
        [+1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, ],
        [+1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, ],
        [+1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, -1, ],
        [-1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, ],
        [+1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, ],
        [-1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, ],
        [-1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1,
         -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, ],
        [-1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1,
         -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1,
         +1, +1, -1, +1, ],
        [-1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1,
         +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, +1,
         +1, +1, +1, -1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1,
         +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1,
         +1, +1, +1, +1, ],
        [-1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1,
         -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1,
         -1, +1, +1, +1, ],
        [+1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1, -1,
         -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1,
         -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1,
         -1, -1, +1, +1, ],
        [-1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1, -1,
         -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1,
         -1, -1, -1, +1, ],
        [+1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, -1,
         -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1,
         -1, -1, -1, -1, ],
        [+1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1,
         -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1, -1,
         -1, -1, -1, -1, ],
        [-1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, -1,
         -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1, +1,
         -1, -1, -1, -1, ],
        [-1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1,
         -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1, -1,
         +1, -1, -1, -1, ],
        [+1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1, +1,
         -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1, -1,
         -1, +1, -1, -1, ],
        [-1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1, -1,
         -1, -1, +1, -1, ],
        [+1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1, +1,
         -1, -1, -1, +1, ],
        [-1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1, -1,
         -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1, -1,
         +1, -1, -1, -1, ],
        [-1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, +1, +1, -1, +1, -1, -1, +1,
         -1, -1, +1, +1, -1, -1, -1, -1, -1, -1, -1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1,
         +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, -1,
         -1, +1, -1, -1, ],
        [-1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, ],
        [-1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, ],
        [+1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, ],
        [+1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, ],
        [+1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, ],
        [-1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, ],
        [-1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, ],
        [-1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, ],
        [-1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, ],
        [+1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, +1, ],
        [-1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, ],
        [+1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, ],
        [+1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, ],
        [-1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, -1, ],
        [+1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, ],
        [+1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, ],
        [-1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, -1, +1, ],
        [-1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1,
         -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, -1, ],
        [+1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1,
         -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, ],
        [+1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, ],
        [+1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1,
         -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1,
         +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, -1, ],
        [+1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1,
         +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1,
         +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, ],
        [+1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1,
         +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1,
         +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, ],
        [+1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1,
         +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, ],
        [+1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1,
         +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, ],
        [+1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1,
         +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1,
         +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, ],
        [-1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1,
         +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1,
         +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, ],
        [-1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1,
         -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1,
         +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, ],
        [+1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1,
         -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1,
         +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1,
         -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1,
         +1, -1, +1, -1, ],
        [+1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1,
         -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1,
         +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1,
         +1, +1, -1, +1, ],
        [-1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1,
         +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1,
         +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1, +1,
         +1, +1, +1, -1, ],
        [+1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1,
         +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1,
         -1, +1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1,
         +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1,
         +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1, -1,
         +1, +1, +1, +1, ],
        [+1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1, +1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1,
         +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1, +1,
         +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1, -1,
         -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1, -1,
         -1, +1, +1, +1, ],
        [-1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1, +1,
         +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1, -1,
         +1, +1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1, +1,
         -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1, +1,
         -1, -1, +1, +1, ],
        [+1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, +1,
         +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1, -1,
         -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1, +1,
         +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1, +1,
         +1, -1, -1, +1, ],
        [-1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1,
         +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1, +1,
         -1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, +1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1, -1,
         +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1, -1,
         +1, +1, -1, -1, ],
        [-1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1,
         -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1, -1,
         +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1, -1,
         +1, -1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1, +1,
         -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1, -1,
         -1, +1, +1, -1, ],
        [-1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1, -1,
         +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1, +1,
         -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1, +1,
         +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1, -1,
         -1, -1, +1, +1, ],
        [-1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, +1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1, -1,
         +1, -1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1, +1,
         +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1, +1,
         -1, -1, -1, +1, ],
        [+1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1, +1,
         +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1, -1,
         -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1, -1,
         -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, -1,
         +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1, -1,
         +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1, -1,
         +1, -1, -1, -1, ],
        [+1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, +1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1, -1,
         -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1, -1,
         -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1,
         -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1, +1,
         -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1, -1,
         -1, +1, -1, -1, ],
        [+1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1, -1,
         -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1, -1,
         -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1, -1,
         +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, +1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1, -1,
         +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1, -1,
         -1, -1, +1, -1, ],
        [-1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, +1, -1, +1, +1, -1, -1, +1, +1, +1, +1, +1, +1, +1, +1, -1, -1, +1,
         +1, -1, +1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, -1, +1,
         -1, -1, -1, -1, +1, -1, +1, +1, +1, +1, -1, +1, -1, -1, -1, -1, +1, -1, +1, -1, +1, +1, -1, -1, +1, -1, +1, -1,
         -1, -1, +1, -1, +1, +1, +1, +1, +1, +1, -1, -1, -1, -1, +1, -1, +1, -1, -1, +1, +1, -1, +1, +1, -1, +1, +1, -1,
         -1, +1, -1, +1, -1, -1, -1, -1, +1, +1, +1, +1, +1, +1, -1, +1, +1, -1, -1, -1, +1, +1, -1, -1, +1, +1, +1, +1,
         -1, +1, -1, +1, +1, +1, -1, +1, +1, -1, -1, +1, +1, -1, +1, +1, +1, -1, +1, -1, +1, +1, +1, +1, -1, -1, +1, +1,
         -1, -1, -1, +1, ],
    ])
