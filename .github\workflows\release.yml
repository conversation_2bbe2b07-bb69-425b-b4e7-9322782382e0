name: Release

run-name: "${{ github.event.inputs.title }}"

defaults:
  run:
    shell: bash -le {0}
on:
  release:
    types: [ published ]
  repository_dispatch:
  workflow_dispatch:
    inputs:
      title:
        description: 'set a title for this run'
        required: false
        default: ''
      repo:
        description: 'GitHub repo {owner}/{repo}'
        required: false
        default: ''
      ref:
        description: 'GitHub ref: Branch, Tag or Commit SHA'
        required: false
        default: ''
      pr_number:
        description: 'PR Number'
        required: false
        type: number
      target:
        description: 'CUDA Torch Python version separated by space, check http://**********/gpu/runner/docker to get all supported combinations'
        required: false
        default: ''
      max-parallel:
        description: 'max parallel jobs'
        required: false
        default: '10'
      upload_release:
        description: 'upload to release (it only works with a tag ref)'
        type: boolean
        required: false
        default: false
      upload_pypi:
        description: 'upload to PyPI'
        type: boolean
        required: false
        default: false
      github_vm:
        description: 'release source on github vm'
        type: boolean
        required: false
        default: false

env:
  CUDA_DEVICE_ORDER: PCI_BUS_ID
  RUNNER: ***********
  TORCH_CUDA_ARCH_LIST: '8.0 8.6 8.9 9.0'
  RELEASE_MODE: 1
  CI: 1
  GPTQMODEL_FORCE_BUILD: 1
  repo: ${{ github.event.inputs.repo || github.repository }}
  ref: ${{ github.event.inputs.ref || github.ref }}
  MAX_JOBS: 8

concurrency:
  group: ${{ github.event.inputs.ref || github.ref }}-workflow-release
  cancel-in-progress: true

jobs:
  check-vm:
    runs-on: [ self-hosted, Linux ]
    container:
      image: modelcloud/gptqmodel:alpine-ci-v1
    outputs:
      ip: ${{ steps.get_ip.outputs.ip }}
      task_list: ${{ steps.assign.outputs.task_list }}
      max-parallel: ${{ steps.get_ip.outputs.max-parallel }}
    if: github.event.inputs.github_vm == 'false'
    steps:
      - name: Checkout Codes
        uses: actions/checkout@v4
        with:
          repository: ${{ env.repo }}
          ref: ${{ env.ref }}

      - name: Print env
        run: |
          echo "event name: ${{ github.event_name }}"
          echo "repo: ${{ env.repo }}"
          echo "ref: ${{ env.ref }}"
          echo "max-parallel: ${{ inputs.max-parallel }}"
          echo "upload_release: ${{ inputs.upload_release }}"
          echo "upload_pypi: ${{ inputs.upload_pypi }}"

      - name: Select server
        id: get_ip
        run: |
          echo "ip=${RUNNER}" >> "$GITHUB_OUTPUT"
          echo "GPU_IP=${RUNNER}" >> $GITHUB_ENV
          echo "ip: $ip"

          max_p=${{ github.event.inputs.max-parallel }}
          max_p="{\"size\": ${max_p:-10}}"
          echo "max-parallel=$max_p" >> "$GITHUB_OUTPUT"
          echo "max-parallel=$max_p"

  release:
    strategy:
      fail-fast: false
      max-parallel: ${{ fromJson(needs.check-vm.outputs.max-parallel).size || 10 }}
      matrix:
        include:
          - cuda: 128
            torch: 2.7.0
            python: 313
          - cuda: 128
            torch: 2.6.0
            python: 313
          - cuda: 128
            torch: 2.7.0
            python: 312
          - cuda: 128
            torch: 2.6.0
            python: 312
          - cuda: 128
            torch: 2.7.0
            python: 311
          - cuda: 128
            torch: 2.6.0
            python: 311
          - cuda: 128
            torch: 2.7.0
            python: 310
          - cuda: 128
            torch: 2.6.0
            python: 310
          - cuda: 128
            torch: 2.7.0
            python: 39
          - cuda: 128
            torch: 2.6.0
            python: 39
          - cuda: 126
            torch: 2.7.0
            python: 313
          - cuda: 126
            torch: 2.6.0
            python: 313
          - cuda: 126
            torch: 2.7.0
            python: 312
          - cuda: 126
            torch: 2.6.0
            python: 312
          - cuda: 126
            torch: 2.7.0
            python: 311
          - cuda: 126
            torch: 2.6.0
            python: 311
          - cuda: 126
            torch: 2.7.0
            python: 310
          - cuda: 126
            torch: 2.6.0
            python: 310
          - cuda: 126
            torch: 2.7.0
            python: 39
          - cuda: 126
            torch: 2.6.0
            python: 39
          - cuda: 124
            torch: 2.5.1
            python: 313
          - cuda: 124
            torch: 2.5.1
            python: 312
          - cuda: 124
            torch: 2.5.1
            python: 311
          - cuda: 124
            torch: 2.5.1
            python: 310
          - cuda: 124
            torch: 2.5.1
            python: 39
          - cuda: 121
            torch: 2.5.1
            python: 313
          - cuda: 121
            torch: 2.5.1
            python: 312
          - cuda: 121
            torch: 2.4.1
            python: 312
          - cuda: 121
            torch: 2.3.1
            python: 312
          - cuda: 121
            torch: 2.2.2
            python: 312
          - cuda: 121
            torch: 2.5.1
            python: 311
          - cuda: 121
            torch: 2.4.1
            python: 311
          - cuda: 121
            torch: 2.3.1
            python: 311
          - cuda: 121
            torch: 2.2.2
            python: 311
          - cuda: 121
            torch: 2.1.2
            python: 311
          - cuda: 121
            torch: 2.5.1
            python: 310
          - cuda: 121
            torch: 2.4.1
            python: 310
          - cuda: 121
            torch: 2.3.1
            python: 310
          - cuda: 121
            torch: 2.2.2
            python: 310
          - cuda: 121
            torch: 2.1.2
            python: 310
          - cuda: 121
            torch: 2.5.1
            python: 39
          - cuda: 121
            torch: 2.4.1
            python: 39
          - cuda: 121
            torch: 2.3.1
            python: 39
          - cuda: 121
            torch: 2.2.2
            python: 39
          - cuda: 121
            torch: 2.1.2
            python: 39
          - cuda: 118
            torch: 2.5.1
            python: 313
          - cuda: 118
            torch: 2.5.1
            python: 312
          - cuda: 118
            torch: 2.4.1
            python: 312
          - cuda: 118
            torch: 2.3.1
            python: 312
          - cuda: 118
            torch: 2.2.2
            python: 312
          - cuda: 118
            torch: 2.5.1
            python: 311
          - cuda: 118
            torch: 2.4.1
            python: 311
          - cuda: 118
            torch: 2.3.1
            python: 311
          - cuda: 118
            torch: 2.2.2
            python: 311
          - cuda: 118
            torch: 2.1.2
            python: 311
          - cuda: 118
            torch: 2.0.1
            python: 311
          - cuda: 118
            torch: 2.5.1
            python: 310
          - cuda: 118
            torch: 2.4.1
            python: 310
          - cuda: 118
            torch: 2.3.1
            python: 310
          - cuda: 118
            torch: 2.2.2
            python: 310
          - cuda: 118
            torch: 2.1.2
            python: 310
          - cuda: 118
            torch: 2.0.1
            python: 310
          - cuda: 118
            torch: 2.5.1
            python: 39
          - cuda: 118
            torch: 2.4.1
            python: 39
          - cuda: 118
            torch: 2.3.1
            python: 39
          - cuda: 118
            torch: 2.2.2
            python: 39
          - cuda: 118
            torch: 2.1.2
            python: 39
          - cuda: 118
            torch: 2.0.1
            python: 39
    runs-on: [ self-hosted, xeon5 ]
    needs:
      - check-vm
    container:
      image: **********:5000/nvidia/cuda:${{ matrix.cuda }}-ubuntu22.04
      volumes:
        - /home/<USER>/models/pyenv:/opt/pyenv
    steps:
      - name: Checkout Codes
        uses: actions/checkout@v4
        with:
          repository: ${{ env.repo }}
          ref: ${{ env.ref }}

      - name: Fetch PR by number
        if: ${{ github.event.inputs.pr_number != 0 }}
        run: |
          PR_NUMBER=${{ github.event.inputs.pr_number }}
          echo "pr number $PR_NUMBER"
          git config --global --add safe.directory $(pwd)
          git fetch origin pull/${PR_NUMBER}/head:pr-${PR_NUMBER}
          git checkout pr-${PR_NUMBER}

      - name: Print Env
        run: |
          python_version=${{ matrix.python }}
          if [[ "$python_version" != *"."* ]]; then
            python_version="${python_version/3/3.}"
          fi
          env_name="cu${{ matrix.cuda }}_torch${{ matrix.torch }}_py$python_version"
          pyenv local $env_name
          pyenv activate $env_name
          echo "== pyenv =="
          pyenv versions
          echo "== python =="
          python --version
          echo "== nvcc =="
          #nvcc --version
          echo "== torch =="
          pip show torch

      - name: Install requirements
        run: |
          # bash -c "$(curl -L http://${RUNNER}/scripts/env/init_compiler.sh)" @ $cuda_version $torch_version $python_version

      - name: Compile
        run: python setup.py bdist_wheel

      - name: Test install
        run: |
          ls -ahl dist
          whl=$(ls -t dist/*.whl | head -n 1 | xargs basename)
          echo "WHL_NAME=$whl" >> $GITHUB_ENV

          twine check dist/$whl
          pip install dist/$whl

      - name: Upload wheel
        continue-on-error: true
        run: |
          sha256=$(sha256sum dist/${{ env.WHL_NAME }})
          response=$(curl -s -F "runid=${{ github.run_id }}" -F "repo=${{ env.repo }}" -F "ref=${{ env.ref }}" -F "sha256=$sha256" -F "file=@dist/${{ env.WHL_NAME }}" http://${{ needs.check-vm.outputs.ip }}/gpu/whl/upload)
          if [ "$response" -eq 0 ]; then
            echo "UPLOADED=1" >> $GITHUB_ENV
          fi

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        continue-on-error: ${{ env.UPLOADED == '1' }}
        with:
          overwrite: true
          name: ${{ env.WHL_NAME }}
          path: dist/${{ env.WHL_NAME }}

      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v2
        if: (github.event_name == 'release' || github.event.inputs.upload_release == 'true') && !cancelled()
        with:
          repo_name: ${{ env.repo }}
          tag: ${{ env.ref }}
          file: dist/${{ env.WHL_NAME }}
          file_glob: true
          overwrite: true

  release-source:
    runs-on: [ self-hosted, xeon5 ]
    needs:
      - check-vm
    container:
      image: ${{ needs.check-vm.outputs.ip }}:5000/modelcloud/gptqmodel:compiler_cuda124-torch2.4.1-python311
    env:
      RELEASE_MODE: 0
    steps:
      - name: Checkout Codes
        uses: actions/checkout@v4
        with:
          repository: ${{ env.repo }}
          ref: ${{ env.ref }}

      - name: Fetch PR by number
        if: ${{ github.event.inputs.pr_number != 0 }}
        run: |
          PR_NUMBER=${{ github.event.inputs.pr_number }}
          echo "pr number $PR_NUMBER"
          git config --global --add safe.directory $(pwd)
          git fetch origin pull/${PR_NUMBER}/head:pr-${PR_NUMBER}
          git checkout pr-${PR_NUMBER}

      - name: Install requirements
        run: pip install build setuptools -U -i http://${{ needs.check-vm.outputs.ip }}/simple/ --trusted-host ${{ needs.check-vm.outputs.ip }}

      - name: Compile
        run: python -m build --no-isolation --sdist

      - name: Check dist
        run: |
          ls -ahl dist
          whl=$(ls -t dist/*.gz | head -n 1 | xargs basename)
          echo "WHL_NAME=$whl" >> $GITHUB_ENV

          twine check dist/$whl

      - name: Upload to local
        continue-on-error: true
        run: |
          sha256=$(sha256sum dist/${{ env.WHL_NAME }})
          response=$(curl -s -F "runid=${{ github.run_id }}" -F "repo=${{ env.repo }}" -F "ref=${{ env.ref }}" -F "sha256=$sha256" -F "file=@dist/${{ env.WHL_NAME }}" http://${{ needs.check-vm.outputs.ip }}/gpu/whl/upload)
          if [ "$response" -eq 0 ]; then
            echo "UPLOADED=1" >> $GITHUB_ENV
          fi

      - name: Upload to artifact
        uses: actions/upload-artifact@v4
        continue-on-error: ${{ env.UPLOADED == '1' }}
        with:
          name: ${{ env.WHL_NAME }}
          path: dist/${{ env.WHL_NAME }}

      - name: Upload package to release
        uses: svenstaro/upload-release-action@v2
        if: (github.event_name == 'release' || github.event.inputs.upload_release == 'true') && !cancelled()
        with:
          file: dist/${{ env.WHL_NAME }}
          tag: ${{ env.ref }}
          file_glob: true
          overwrite: true

      - name: Waiting for confirmation
        if: (github.event_name == 'release' || github.event.inputs.upload_pypi == 'true') && !cancelled()
        run: |
          timestamp=$(date +%s%3N)

          echo "open http://${RUNNER}/gpu/ci/confirm?id=${{ github.run_id }}&timestamp=$timestamp&confirmed=1 to confirm releasing to pypi"
          for i in {1..5}; do echo "."; done
          echo "click http://${RUNNER}/gpu/ci/confirm?id=${{ github.run_id }}&timestamp=$timestamp&denied=1 to DENY"

          status=-1

          while [ "$status" -lt 0 ]; do
            status=$(curl -s "http://${RUNNER}/gpu/ci/confirm?id=${{ github.run_id }}&timestamp=$timestamp")
            if [ "$status" == "2" ]; then
                echo "PYPI_RELEASE_CONFIRMATION=$status" >> $GITHUB_ENV
            elif [ "$status" -lt 0 ]; then
              sleep 5
            else
              echo "release has been confirmed"
              echo "PYPI_RELEASE_CONFIRMATION=$status" >> $GITHUB_ENV
            fi
          done

      - name: Upload sdist to pypi
        if: (github.event_name == 'release' || github.event.inputs.upload_pypi == 'true') && env.PYPI_RELEASE_CONFIRMATION == '1' && !cancelled()
        env:
          TWINE_USERNAME: "__token__"
          TWINE_PASSWORD: ${{ secrets.PYPI_KEY }}
        run: |
          python -m twine upload dist/*gz


  release-source-github:
    runs-on: ubuntu-24.04
    env:
      RELEASE_MODE: 0
      BUILD_CUDA_EXT: '0'
    if: github.event.inputs.github_vm == 'true'
    steps:
      - name: Checkout Codes
        uses: actions/checkout@v4
        with:
          repository: ${{ env.repo }}
          ref: ${{ env.ref }}

      - name: Fetch PR by number
        if: ${{ github.event.inputs.pr_number != 0 }}
        run: |
          PR_NUMBER=${{ github.event.inputs.pr_number }}
          echo "pr number $PR_NUMBER"
          git config --global --add safe.directory $(pwd)
          git fetch origin pull/${PR_NUMBER}/head:pr-${PR_NUMBER}
          git checkout pr-${PR_NUMBER}

      - uses: actions/setup-python@v5
        with:
          python-version: 3.13
          cache: 'pip'

      - name: Install requirements
        run: |
          pip install build setuptools uv -U
          uv pip install torch twine --system

      - name: Compile
        run: python -m build --no-isolation --sdist

      - name: Check dist
        run: |
          ls -ahl dist
          whl=$(ls -t dist/*.gz | head -n 1 | xargs basename)
          echo "WHL_NAME=$whl" >> $GITHUB_ENV

          twine check dist/$whl

      - name: Upload to artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.WHL_NAME }}
          path: dist/${{ env.WHL_NAME }}

      - name: Upload package to release
        uses: svenstaro/upload-release-action@v2
        if: (github.event_name == 'release' || github.event.inputs.upload_release == 'true') && !cancelled()
        with:
          file: dist/${{ env.WHL_NAME }}
          tag: ${{ env.ref }}
          file_glob: true
          overwrite: true

      - name: Waiting for confirmation
        if: (github.event_name == 'release' || github.event.inputs.upload_pypi == 'true') && !cancelled()
        run: |
          for i in {1..5}; do sleep 5; done

      - name: Upload sdist to pypi
        if: (github.event_name == 'release' || github.event.inputs.upload_pypi == 'true') && !cancelled()
        env:
          TWINE_USERNAME: "__token__"
          TWINE_PASSWORD: ${{ secrets.PYPI_KEY }}
        run: |
          python -m twine upload dist/*gz
