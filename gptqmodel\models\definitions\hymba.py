# Copyright 2024-2025 ModelCloud.ai
# Copyright 2024-2025 <EMAIL>
# Contact: <EMAIL>, x.com/qubitium
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from ..base import BaseGPTQModel


class HymbaGPTQ(BaseGPTQModel):
    supports_desc_act = [False]
    require_trust_remote_code = True
    require_monkeypatch = True
    require_pkgs_version = ["tiktoken>=0.7.0",
                            "sentencepiece>=0.2.0",
                            "protobuf>=5.28.3",
                            "ninja>=1.11.1.1",
                            "einops>=0.8.0",
                            "mamba_ssm>=2.2.2",
                            "causal_conv1d>=1.4.0",
                            "attn_gym>=0.0.3.dev5"]

    base_modules = ["model.embed_tokens", "model.final_layernorm"]
    pre_lm_head_norm_module = "model.final_layernorm"

    layers_node = ["model.layers"]
    layer_type = "HymbaDecoderLayer"
    layer_modules = [
        ["mamba.in_proj"],
        ["mamba.out_proj"],
        # ["mamba.x_proj.0"],
        # ["mamba.dt_proj.0"], TODO We need to add auto pad to TritonV2QuantLinear before we can quantify the Module.
        ["moe.experts.0.up_proj", "moe.experts.0.gate_proj"],
        ["moe.experts.0.down_proj"],
    ]

    def monkey_patch(self):
        if hasattr(self.config, 'conv_dim'):
            new_conv_dim = {}
            try:
                for k, v in self.config.conv_dim.items():
                    if isinstance(k, str):
                        new_conv_dim[int(k)] = v
                self.config.conv_dim = new_conv_dim
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError("The key of HymbaConfig.conv_dim should be a string of numbers.")
