# Copyright 2024-2025 ModelCloud.ai
# Copyright 2024-2025 <EMAIL>
# Contact: <EMAIL>, x.com/qubitium
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from ..base import BaseGPTQModel


class ExaoneGPTQ(BaseGPTQModel):
    # exaone requires custom model code
    require_trust_remote_code = True

    base_modules = ["transformer.ln_f", "transformer.wte"]
    pre_lm_head_norm_module = "transformer.ln_f"

    layers_node = ["transformer.h"]
    layer_type = "ExaoneBlock"
    layer_modules = [
        ["attn.attention.k_proj", "attn.attention.v_proj", "attn.attention.q_proj"],
        ["attn.attention.out_proj"],
        ["mlp.c_fc_0", "mlp.c_fc_1"],
        ["mlp.c_proj"],
    ]
