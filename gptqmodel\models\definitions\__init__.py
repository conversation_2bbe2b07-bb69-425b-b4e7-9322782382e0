# isort: off
# Copyright 2024-2025 ModelCloud.ai
# Copyright 2024-2025 <EMAIL>
# Contact: <EMAIL>, x.com/qubitium
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Many model architectures inherit from LlamaGPTQ, so it’s necessary to import llama first to avoid circular imports.
from .llama import LlamaGPTQ

# other model
from .baichuan import BaiChuanGPTQ
from .bloom import BloomGPTQ
from .chatglm import ChatGLM
from .codegen import CodeGenGPTQ
from .cohere import CohereGPTQ
from .cohere2 import Cohere2GPTQ
from .dbrx import DbrxGPTQ
from .dbrx_converted import DbrxConvertedGPTQ
from .decilm import DeciLMGPTQ
from .deepseek_v2 import DeepSeekV2GPTQ
from .deepseek_v3 import DeepSeekV3GPTQ
from .dream import DreamGPTQ
from .exaone import ExaoneGPTQ
from .ernie4_5 import ERNIE4_5GPTQ
from .ernie4_5_moe import ERNIE4_5_MOEGPTQ
from .gemma import GemmaGPTQ
from .gemma2 import Gemma2GPTQ
from .gemma3 import Gemma3GPTQ
from .glm import GLM
from .gpt2 import GPT2GPTQ
from .gpt_bigcode import GPTBigCodeGPTQ
from .gpt_neox import GPTNeoXGPTQ
from .gptj import GPTJGPTQ
from .granite import GraniteGPTQ
from .grinmoe import GrinMOEGPTQ
from .hymba import HymbaGPTQ
from .instella import InstellaGPTQ
from .internlm import InternLMGPTQ
from .internlm2 import InternLM2GPTQ
from .llama import LlamaGPTQ
from .longllama import LongLlamaGPTQ
from .mimo import MimoGPTQ
from .minicpm3 import MiniCPM3GPTQ
from .mistral import MistralGPTQ
from .mixtral import MixtralGPTQ
from .mllama import MLlamaGPTQ
from .mobilellm import MobileLLMGPTQ
from .moss import MOSSGPTQ
from .mpt import MPTGPTQ
from .olmo2 import Olmo2GPTQ
from .opt import OPTGPTQ
from .ovis import OvisGPTQ
from .phi import PhiGPTQ
from .phi3 import Phi3GPTQ
from .qwen import QwenGPTQ
from .qwen2 import Qwen2GPTQ
from .qwen2_5_vl import Qwen2_5_VLGPTQ
from .qwen2_moe import Qwen2MoeGPTQ
from .qwen2_vl import Qwen2VLGPTQ
from .qwen3 import Qwen3GPTQ
from .qwen3_moe import Qwen3MoeGPTQ
from .rw import RWGPTQ
from .stablelmepoch import StableLMEpochGPTQ
from .starcoder2 import Starcoder2GPTQ
from .telechat2 import TeleChat2GPTQ
from .xverse import XverseGPTQ
from .yi import YiGPTQ
from .falcon_h1 import FalconH1GPTQ
from .pangu_alpha import PanguAlphaGPTQ