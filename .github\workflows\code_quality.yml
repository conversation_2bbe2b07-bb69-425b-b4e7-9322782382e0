name: Ruff Check

on:
  push:
  pull_request:

jobs:
  ruff-check:
    strategy:
      fail-fast: false
      matrix:
        python-version: [3.13]
        os: [ubuntu-24.04]
    runs-on: ${{ matrix.os }}
  
    steps:
    - uses: actions/checkout@v4
      with:
        repository: ${{ github.event.pull_request.head.repo.full_name || github.repository }}
        ref: ${{ github.event.pull_request.head.ref || github.ref }}

    - uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Ruff Check
      run: |
        format/format.sh
